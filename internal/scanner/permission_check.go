// Package scanner -----------------------------
// @file      : permission_check.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/6/3 10:01
// -------------------------------------------
package scanner

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/pkg/apiserver/dto"
	"AgenticAI-Client/pkg/apiserver/handler"
	"context"
	"go.uber.org/zap"
	"time"
)

type PermissionCheck struct {
	ctx           context.Context
	c             *config.PermissionCheckConfig
	logger        *zap.Logger
	permissionSrv handler.PermissionServiceHandler
}

// Deprecated:  NewPermissionCheck 创建权限检查器
func NewPermissionCheck(ctx context.Context, cfg *config.ScannerConfig, _permission handler.PermissionServiceHandler) *PermissionCheck {
	pmc := &PermissionCheck{
		ctx:           ctx,
		c:             &cfg.PermissionCheck,
		logger:        zap.L().With(zap.String("module", "PermissionCheck")),
		permissionSrv: _permission,
	}

	return pmc
}

func (p *PermissionCheck) SetEnable(flag bool) {
	p.c.Disabled = !flag
}

func (p *PermissionCheck) scan() {
	ch := time.NewTicker(p.c.ScanLoopIntervalDuration)
	defer ch.Stop()

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("scan loop exit")
			return
		case <-ch.C:
			if p.c.Disabled {
				continue
			}

			p.doCheck()
		}
	}
}

func (p *PermissionCheck) doCheck() {
	_, err := p.permissionSrv.PermissionRequest(p.ctx, &dto.Null{})
	if err != nil {
		p.logger.Error("permission check error", zap.Error(err))
	}
	//p.logger.Debug("permission check result", zap.String("result", res))
}
