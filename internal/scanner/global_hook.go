// Package scanner -----------------------------
// @file      : global_hook.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/7/20 15:24
// -------------------------------------------
package scanner

import (
	"AgenticAI-Client/internal/ui"
	hook "github.com/robotn/gohook"
)

type GlobalHook struct{}

func NewGlobalHook() *GlobalHook {
	return &GlobalHook{}
}

func (p *GlobalHook) Start() {
	p.startHotkeyListener()
}

// startHotkeyListener 启动全局热键监听
func (p *GlobalHook) startHotkeyListener() {
	hook.Register(hook.KeyDown, []string{"alt", "space"}, func(e hook.Event) {
		ui.GetMainMenu().TaskMenu().ToggleFloatBall()
	})
	s := hook.Start()
	<-hook.Process(s)
}
