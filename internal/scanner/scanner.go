// Package scanner -----------------------------
// @file      : scanner.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/27 16:53
// -------------------------------------------
package scanner

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/pkg/apiserver/handler"
	"AgenticAI-Client/pkg/eventbus"
	"context"
)

type Impl struct {
	ctx    context.Context
	cancel context.CancelCauseFunc
	c      *config.ScannerConfig
	hbp    *HeartbeatReporter
	gHook  *GlobalHook
}

func New(ctx context.Context, c *config.ScannerConfig, _eventBus eventbus.EventBus, _permission handler.PermissionServiceHandler) *Impl {
	ctx, cancel := context.WithCancelCause(ctx)
	_hbp := NewHeartbeatReporter(ctx, c, _eventBus)
	_gHook := NewGlobalHook()
	impl := &Impl{
		ctx:    ctx,
		cancel: cancel,
		c:      c,
		hbp:    _hbp,
		gHook:  _gHook,
	}

	return impl
}

func (p *Impl) Run() {
	go p.hbp.scan()
	go p.gHook.Start()
}

func (p *Impl) Close() {
	p.cancel(nil)
}
