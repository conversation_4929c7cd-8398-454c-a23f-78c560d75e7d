// Package event -----------------------------
// @file      : event.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/22 16:31
// -------------------------------------------
package event

import (
	"github.com/wailsapp/wails/v3/pkg/application"
)

var _app *application.App

func RegisterEvent(app *application.App) {
	_app = app
}

func EmitEvent(name string, data ...any) {
	if _app == nil {
		return
	}
	_app.Event.Emit(name, data...)
}

func EmitInnerUIEvent(name string, data ...any) {
	if _app == nil {
		return
	}
	_app.Event.Emit(name, data...)
}

func OnEvent(name string, callback func(event *application.CustomEvent)) func() {
	return _app.Event.On(name, callback)
}

type DialogStatus uint8

const (
	ShowExitWarningDialog DialogStatus = iota
	ShowExitWarningDialogFromLogout
	ShowExitWarningDialogFromQuit
)

type WindowStatus uint8

const (
	WindowShowHome WindowStatus = iota
	WindowShowNewTask

	WindowShowSetting
	WindowShowFloating
	WindowShowBackdropTransparentPage
	// WindowShowCustomizeUrl 显示自定义链接页面, 登录，登出用到
	WindowShowCustomizeUrl
	WindowHide
)

type PermissionStatus uint8

const (
	PermissionCheck PermissionStatus = iota
	PermissionRequest
	PermissionToAccess
	PermissionToScreen
)

type InnerUIEvent uint8

const (
	// InnerUIEventOpenFloatBall 打开悬浮球
	InnerUIEventOpenFloatBall InnerUIEvent = iota
	// InnerUIEventHideFloatBall 关闭悬浮球
	InnerUIEventHideFloatBall
	// InnerUIEventLogout 退出登录
	InnerUIEventLogout
	// Deprecated: InnerUIEventOpenFloatBallChatMain 打开悬浮球对话框，并传递任务id
	InnerUIEventOpenFloatBallChatMain
	// Deprecated: InnerUIEventHideFloatBallChatMain 关闭悬浮球对话框
	InnerUIEventHideFloatBallChatMain
	// Deprecated: InnerUIEventStartScanMouseMoveGlobalLocation 开启全局鼠标位置监听
	InnerUIEventStartScanMouseMoveGlobalLocation
	// Deprecated: InnerUIEventStopScanMouseMoveGlobalLocation 停止全局鼠标位置监听
	InnerUIEventStopScanMouseMoveGlobalLocation
)
