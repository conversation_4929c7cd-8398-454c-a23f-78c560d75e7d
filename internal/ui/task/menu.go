// Package task -----------------------------
// @file      : menu.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/23 15:24
// -------------------------------------------
package task

import (
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/event"
	"AgenticAI-Client/internal/global"
	"AgenticAI-Client/pkg/service"
	"github.com/wailsapp/wails/v3/pkg/application"
	"go.uber.org/zap"
)

type Menu struct {
	app               *application.App
	systemTray        *application.SystemTray
	menu              *application.Menu
	items             *MenuItems
	isPaused          bool
	logger            *zap.Logger
	floatWindowEnable bool
}

type MenuItems struct {
	loginItem       *application.MenuItem
	controlItem     *application.MenuItem
	floatWindowItem *application.MenuItem
	logoutItem      *application.MenuItem
	quitItem        *application.MenuItem
}

func NewTaskMenu(app *application.App, st *application.SystemTray) *Menu {
	tm := &Menu{
		app:               app,
		systemTray:        st,
		logger:            zap.L().Named("Menu"),
		isPaused:          true,
		floatWindowEnable: false,
	}

	tm.createTaskMenu()
	if service.UserServiceImpl.IsLogin() {
		tm.ShowLogin()
	} else {
		tm.ShowLogout()
	}

	return tm
}

// ShowLogin 显示登录状态下的菜单
func (tm *Menu) ShowLogin() {
	tm.items.loginItem.SetHidden(true)
	tm.items.controlItem.SetHidden(false)
	tm.items.floatWindowItem.SetHidden(false)
	tm.items.logoutItem.SetHidden(false)
	tm.menu.Update()
}

// ShowLogout 显示退出状态下的菜单
func (tm *Menu) ShowLogout() {
	tm.items.loginItem.SetHidden(false)
	tm.items.controlItem.SetHidden(true)
	tm.items.floatWindowItem.SetHidden(true)
	tm.items.logoutItem.SetHidden(true)
	tm.menu.Update()
	// terminate the running task when user logout
	if !tm.isPaused {
		event.EmitEvent(constant.EventTaskChatHandle, StatusFinished)
	}
}

func (tm *Menu) HandleTaskEvent(e *application.CustomEvent) {
	vals, ok := e.Data.([]interface{})
	if !ok {
		return
	}
	if len(vals) == 0 {
		return
	}
	val, ok := vals[0].(service.UserStatus)
	if !ok {
		// from inner UI
		// TypeScript number to Go int
		_val, _ok := vals[0].(float64)
		if !_ok {
			return
		}
		val = service.UserStatus(_val)
	}

	switch val {
	case service.UserStatusLogin:
		tm.ShowLogin()
	case service.UserStatusLogout:
		tm.ShowLogout()
	default:
		tm.logger.Error("unknown user status", zap.Any("status", val))
	}
}

func (tm *Menu) TaskMenu() *application.Menu {
	return tm.menu
}

func (tm *Menu) IsFloatWindowEnabled() bool {
	return tm.floatWindowEnable
}

func (tm *Menu) createTaskMenu() {
	taskMenu := tm.app.NewMenu()

	_loginItem := taskMenu.Add("点击登录	").SetBitmap(global.ReadAssets(constant.NewTaskPic)).OnClick(func(c *application.Context) {
		tm.Login()
	})
	_controlItem := taskMenu.Add("新任务	").SetBitmap(global.ReadAssets(constant.NewTaskPic)).OnClick(func(c *application.Context) {
		event.EmitEvent(constant.EventMainWindow, event.WindowShowNewTask)
	})
	taskMenu.AddSeparator()

	_floatWindowItem := taskMenu.Add("打开悬浮窗	").SetBitmap(global.ReadAssets(constant.FloatWindowHidePic)).OnClick(func(c *application.Context) {
		tm.ToggleFloatBall()
	})
	taskMenu.AddSeparator()

	_logoutItem := taskMenu.Add("退出登录").SetBitmap(global.ReadAssets(constant.LogoutPic)).OnClick(func(c *application.Context) {
		tm.Logout()
	})
	taskMenu.AddSeparator()

	_quitItem := taskMenu.Add("退出TuriX").SetAccelerator("CmdOrCtrl+Q").SetBitmap(global.ReadAssets(constant.ExitPic))

	items := &MenuItems{
		loginItem:       _loginItem,
		controlItem:     _controlItem,
		floatWindowItem: _floatWindowItem,
		logoutItem:      _logoutItem,
		quitItem:        _quitItem,
	}

	tm.items = items
	tm.menu = taskMenu
}

func (tm *Menu) SetPause(flag bool) {
	tm.isPaused = flag
}

func (tm *Menu) GetPause() bool {
	return tm.isPaused
}

// ToggleFloatBall 切换悬浮球显示状态
func (tm *Menu) ToggleFloatBall() {
	if tm.floatWindowEnable {
		// 点击隐藏即隐藏
		tm.items.floatWindowItem.SetLabel("打开悬浮窗	")
		flw, _ := tm.app.Window.Get(constant.WindowFloat)
		flw.Hide()
	} else {
		// 点击显示即显示
		tm.items.floatWindowItem.SetLabel("关闭悬浮窗	")
		flw, _ := tm.app.Window.Get(constant.WindowFloat)
		flw.Show()
		flw.Focus()
	}
	tm.floatWindowEnable = !tm.floatWindowEnable

	// 发送事件告知前端
	event.EmitInnerUIEvent(constant.EventInnerUIToggleBall, tm.floatWindowEnable)
}

// Logout 触发登出处理
func (tm *Menu) Logout() {
	if !tm.isPaused {
		event.EmitEvent(constant.EventDialog, event.ShowExitWarningDialogFromLogout)
		return
	}
	event.EmitEvent(constant.EventLogin, false)
}

// Login 触发登录处理
func (tm *Menu) Login() {
	if !service.UserServiceImpl.IsLogin() {
		event.EmitEvent(constant.EventLogin, true)
		return
	}
}

// SetQuitAppCallbackFunc 设置退出应用回调函数
func (tm *Menu) SetQuitAppCallbackFunc(callback func(c *application.Context)) {
	tm.items.quitItem.OnClick(callback)
}
