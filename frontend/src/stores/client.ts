import * as apiserver from '@client/apiserver/dto'
import * as client from '@client/client'
import * as service from '@client/service'

import { Events } from '@wailsio/runtime'

if (import.meta.env.DEV) {
// @ts-expect-error development only
  window.$client = {
    apiserver,
    client,
    service,
  }
}

interface OnEvent {

}

export const InnerUIEnum = {

} as const

interface EmitEvent {

}

export const useClient = defineStore('client', () => {
  return {
    deviceID: localStorage.getItem('device-id'),
    emit<T extends keyof EmitEvent>(...arg: [T, ...EmitEvent[T][]]) {
      Events.Emit(new Events.WailsEvent(...arg))
    },
    isClient: true,
    on<T extends keyof OnEvent>(type: T, listener: (...args: OnEvent[T]) => void) {
      Events.On(type, (ev) => {
        listener(...(ev.data ?? []) as OnEvent[T])
      })
    },
  }
})
