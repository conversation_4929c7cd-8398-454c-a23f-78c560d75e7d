<script setup lang="ts">
import { usePreset } from '@primeuix/themes'
import { Window } from '@wailsio/runtime'
import AuraSlate from '@/themes/AuraSlate'

usePreset(AuraSlate)
useColorMode().value = 'light'
</script>

<template>
  <div class="size-full bg-gradient-to-br from-yellow-50/95 via-white/95 to-cyan-100/95 flex flex-col justify-center items-center">
    <router-view v-slot="{ Component }">
      <transition
        mode="out-in"
        enter-active-class="animate-fadein overflow-hidden"
        leave-active-class="animate-fadeout overflow-hidden"
      >
        <component :is="Component" />
      </transition>
    </router-view>
  </div>
</template>
