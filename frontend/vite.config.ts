import type { ComponentResolverObject } from 'unplugin-vue-components/types'

import path from 'node:path'
import { fileURLToPath, URL } from 'node:url'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import { PrimeVueResolver } from '@primevue/auto-import-resolver'
import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import * as UasComps from '@vueuse/components'
import AutoImport from 'unplugin-auto-import/vite'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import IconsResolver from 'unplugin-icons/resolver'
import Icons from 'unplugin-icons/vite'
import Components from 'unplugin-vue-components/vite'
import { VueRouterAutoImports } from 'unplugin-vue-router'
import VueRouter from 'unplugin-vue-router/vite'
import { defineConfig, loadEnv } from 'vite'
import devtoolsJson from 'vite-plugin-devtools-json'
import { envParse } from 'vite-plugin-env-parse'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, './env', '')

  return {
    build: {
      // sourcemap: true,
      target: 'esnext',
    },
    envDir: './env',
    plugins: [
      devtoolsJson(),
      VueI18nPlugin({
        include: [path.resolve(__dirname, './src/locales/*.json')],
      }),
      tailwindcss(),
      VueRouter({
        beforeWriteFiles: function beforeWriteFiles(root) {
          root.children.forEach(beforeWriteFiles)
          if (root.children.some(({ fullPath }) => !fullPath.endsWith(('/:path(.*)')))) {
            root.insert(':path(.*)', fileURLToPath(new URL('./src/components/page/404.vue', import.meta.url)).replaceAll('\\', '/'))
          }
        },
        dts: './types/auto/typed-router.d.ts',
        routesFolder: [
          {
            path: '',
            src: 'src/views',
          },
        ],
      }),
      vue(),
      vueJsx(),
      Components({
        directoryAsNamespace: true,
        dirs: ['src/components', 'src/api/components'],
        dts: './types/auto/components.d.ts',
        resolvers: [
          PrimeVueResolver({
            components: {
              prefix: 'P',
            },
          }),
          IconsResolver({ customCollections: ['app-icons'], prefix: 'i' }),
          (name) => {
            if (
              Object.keys(UasComps)
                .filter(key => !key.startsWith('v'))
                .includes(name)
            ) {
              return { from: '@vueuse/components', name }
            }
          },
          {
            resolve: (name) => {
              if (
                Object.keys(UasComps)
                  .filter(key => key.startsWith('v'))
                  .includes(`v${name}`)
              ) {
                return { from: '@vueuse/components', name: `v${name}` }
              }
            },
            type: 'directive',
          },
          ...Object.entries({
            '@formkit/auto-animate': ['vAutoAnimate'],
          } as Record<string, string[] | [string, string][]>).map(([from, items]) => ({
            resolve(name) {
              const item = items.find((item) => {
                return (typeof item === 'string' ? item : item[1]) === `v${name}`
              })
              if (item) {
                return typeof item === 'string'
                  ? {
                      from,
                      name: item,
                    }
                  : {
                      as: item[1],
                      from,
                      name: item[0],
                    }
              }
            },
            type: 'directive',
          }) as ComponentResolverObject),

          ...Object.entries({
            'motion-v': [['Motion', 'VueMotion']],
          } as Record<string, string[] | [string, string][]>).map(([from, items]) => ({
            resolve(name) {
              const item = items.find((item) => {
                return (typeof item === 'string' ? item : item[1]) === name
              })
              if (item) {
                return typeof item === 'string'
                  ? {
                      from,
                      name: item,
                    }
                  : {
                      as: item[1],
                      from,
                      name: item[0],
                    }
              }
            },
            type: 'component',
          }) as ComponentResolverObject),
        ],
      }),
      AutoImport({
        dts: './types/auto/auto-imports.d.ts',
        eslintrc: {
          enabled: true, // Default `false`
          filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
          globalsPropValue: 'readonly', // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },
        imports: [
          '@vueuse/core',
          '@vueuse/math',
          '@vueuse/head',
          'vue',
          'pinia',
          {
            '@/api/index.ts': [['default', '$api']],
            '@/api/useRequest': ['$req'],
            '@/locales/index.ts': [['global', '$i18n']],
            '@/stores/message.ts': ['$message'],
            'vue-i18n': ['useI18n'],
          },
          VueRouterAutoImports,
        ],
      }),
      Icons({
        autoInstall: true,
        compiler: 'vue3',
        customCollections: {
          // a helper to load icons from the file system
          // files under `./assets/icons` with `.svg` extension will be loaded as it's file name
          // you can also provide a transform callback to change each icon (optional)
          'app-icons': FileSystemIconLoader('./src/icons', svg =>
            svg.replace(/^<svg /, '<svg fill="currentColor" ')),
        },
        scale: 1,
      }),
      envParse({
        dtsPath: './types/auto/import-meta.d.ts',
      }),
      vueDevTools(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@client': fileURLToPath(new URL('./bindings/AgenticAI-Client/pkg', import.meta.url)),

      },
    },
    server: {
      proxy: {
        '/api': {
          changeOrigin: true,
          secure: false,
          target: env.server_proxy_target,
        },
      },
    },
  }
})
