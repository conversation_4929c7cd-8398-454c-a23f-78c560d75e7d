package client

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/event"
	"AgenticAI-Client/internal/ui"
	"AgenticAI-Client/internal/ui/task"
	"AgenticAI-Client/pkg/client/cache"
	"AgenticAI-Client/pkg/eventbus"
	"AgenticAI-Client/pkg/oss"
	"AgenticAI-Client/pkg/service"
	"AgenticAI-Client/pkg/utils"
	pb "AgenticAI-Client/pkg/workflow/gen"
	"AgenticAI-Client/pkg/workflow/message"
	"context"
	"errors"
	"fmt"
	"io"

	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
	"github.com/wailsapp/wails/v3/pkg/application"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc"
)

type AIClient struct {
	ctx           context.Context
	cfg           *config.TuriXConfig
	conn          *grpc.ClientConn
	chatSrvClient pb.ChatServiceClient
	eventBus      eventbus.EventBus
	logger        *zap.Logger

	sendCh         chan *message.TaskRequest
	isChatRunning  bool
	currentTaskID  string
	cache          *cache.TaskCache
	ossClient      *oss.Client
	isAbnormalQuit bool
	menu           *ui.MainMenu
}

func NewAIClient(
	ctx context.Context,
	c *config.TuriXConfig,
	_eventBus eventbus.EventBus,
	oss *oss.Client,
) (*AIClient, error) {
	conn, err := utils.NewGrpcClient(c.AIAgentServer.Host, c.AIAgentServer.Port)
	if err != nil {
		return nil, err
	}

	taskCache, err := cache.NewTaskCache(c.Path.DataDir)
	if err != nil {
		return nil, err
	}

	var (
		_currentTaskID  string
		_isAbnormalQuit bool
	)
	if taskCache != nil && len(taskCache.TReq) > 0 && len(taskCache.TReq[0].TaskId) > 0 {
		_isAbnormalQuit = true
		_currentTaskID = taskCache.TReq[0].TaskId
	}

	ac := &AIClient{
		ctx:            ctx,
		conn:           conn,
		chatSrvClient:  pb.NewChatServiceClient(conn),
		eventBus:       _eventBus,
		logger:         zap.L().With(zap.String("module", "AIClient")),
		sendCh:         make(chan *message.TaskRequest, 5),
		ossClient:      oss,
		cache:          taskCache,
		currentTaskID:  _currentTaskID,
		isAbnormalQuit: _isAbnormalQuit,
		menu:           ui.GetMainMenu(),
	}

	// run updater
	service.UpdateServiceImpl.Run()
	return ac, nil
}

func (c *AIClient) HandleChatRequest(e *application.CustomEvent) {
	vals, ok := e.Data.([]interface{})
	if !ok {
		return
	}
	if len(vals) == 0 {
		return
	}
	val, ok := vals[0].(task.Status)
	if !ok {
		return
	}

	var (
		commandType emqx.CommandType
		isQuit      bool
	)
	switch val {
	case task.StatusRunning:
		commandType = emqx.START
	case task.StatusStopping:
		commandType = emqx.STOP
	case task.StatusReRunning:
		commandType = emqx.RESUME
	case task.StatusFinished:
		commandType = emqx.END
	case task.StatusQuit:
		commandType = emqx.END
		isQuit = true
	case task.StatusError:
		commandType = emqx.END
	default:
		return
	}

	req := message.NewTaskRequest(c.currentTaskID, "", service.UserServiceImpl.GetDeviceID(), commandType, isQuit, "")
	err := c.StreamResponse(c.ctx, req)
	if err != nil {
		c.clearTaskCache()
		zap.L().Error("HandleChatRequest event from manu ui", zap.Error(err))
	}
}

func (c *AIClient) HandleBrokerChatRequest(tr *message.TaskRequest) error {
	if tr == nil {
		return nil
	}

	// 更新menu ui
	request := tr.EMQXPayload
	if request.Command == emqx.START {
		event.EmitEvent(constant.EventSystemTrayMenuChange, task.StatusRunning)
	} else if request.Command == emqx.RESUME {
		event.EmitEvent(constant.EventSystemTrayMenuChange, task.StatusReRunning)
	} else if request.Command == emqx.STOP {
		event.EmitEvent(constant.EventSystemTrayMenuChange, task.StatusStopping)
	} else if request.Command == emqx.END {
		event.EmitEvent(constant.EventSystemTrayMenuChange, task.StatusFinished)
	} else {
		return nil
	}

	if request.Command == emqx.START {
		if c.menu.TaskMenu().IsFloatWindowEnabled() {
			c.currentTaskID = tr.Payload.GetConversationId()
			// 显示悬浮窗对话框
			//if !c.windows.FloatBallChatMainWindow().IsVisible() {
			//	c.windows.FloatBallChatMainWindow().Window().Show()
			//}
		}
	}

	c.setupTaskCache(&request)
	err := c.StreamResponse(c.ctx, tr)
	if err != nil {
		c.clearTaskCache()
		return err
	}
	return nil
}

func (c *AIClient) HandleConnectHandler() error {
	if c.isAbnormalQuit {
		req := message.NewTaskRequest(c.currentTaskID, "", service.UserServiceImpl.GetDeviceID(), emqx.END, false, "")
		err := c.StreamResponse(c.ctx, req)
		if err != nil {
			return err
		}
		c.isAbnormalQuit = false
		c.currentTaskID = ""
	}

	return nil
}

func (c *AIClient) StreamResponse(ctx context.Context, req *message.TaskRequest) error {
	taskID := req.Payload.GetConversationId()
	if c.isChatRunning && c.currentTaskID != taskID {
		return nil
	}
	c.currentTaskID = taskID

	if !c.isChatRunning {
		c.isChatRunning = true
		go c.startChatStream()
	}

	select {
	case c.sendCh <- req:
		return nil
	default:
		// 通道满，可选重试或返回错误
		return fmt.Errorf("chat service is busy, please try again later")
	}
}

func (c *AIClient) startChatStream() {
	defer func() {
		c.isChatRunning = false
	}()

	var postHandler func(req *message.TaskRequest, cancel context.CancelFunc)
	postHandler = func(req *message.TaskRequest, cancel context.CancelFunc) {
		if c.cache != nil {
			if req == nil {
				req = &message.TaskRequest{}
			}
			c.cache.TRsp.IsEnd = true
			c.cache.TRsp.Status = emqx.CompletedStatus
			c.cache.TRsp.TaskId = c.currentTaskID
			if len(c.cache.TRsp.Steps) > 0 {
				if c.cache.TRsp.Steps[len(c.cache.TRsp.Steps)-1] != nil {
					c.cache.TRsp.Steps[len(c.cache.TRsp.Steps)-1].Status = emqx.CompletedStatus
				}
			}
			rsp := c.cache.TRsp
			reply := &message.TaskResponse{
				IsQuit:  req.IsQuit,
				Payload: rsp,
			}
			c.eventBus.EmitChatResponse(reply)
			cancel()
		}
	}

	var interFunc func() error
	interFunc = func() error {
		// 建立流
		streamCtx, cancel := context.WithCancel(c.ctx)
		stream, err := c.chatSrvClient.Chat(streamCtx)
		if err != nil {
			postHandler(nil, cancel)
			return fmt.Errorf("start chat failed with %w", err)
		}

		g, _ := errgroup.WithContext(streamCtx)

		// 发送 goroutine
		g.Go(func() error {
			for {
				select {
				case <-streamCtx.Done():
					return streamCtx.Err()
				case req, ok := <-c.sendCh:
					if !ok {
						postHandler(req, cancel)
						err = stream.CloseSend()
						if err != nil {
							return fmt.Errorf("stream.CloseSend failed with %w", err)
						}
						return nil
					}

					payload := &req.Payload
					c.logger.Debug("send message to agent", zap.String("prompt", payload.Prompt), zap.String("conversation_id", payload.ConversationId), zap.Int("CommandType", int(payload.Command)))
					err = stream.Send(payload)
					if err != nil {
						postHandler(req, cancel)
						if err == io.EOF {
							return nil
						}
						return fmt.Errorf("stream.Send failed failed with %w", err)
					}

					// handle end control command
					if req.Payload.Command == pb.CommandType_END {
						postHandler(req, cancel)
					}
				}
			}
		})

		// 接收 goroutine
		g.Go(func() error {
			for {
				var chunk *pb.ChatReply
				chunk, err = stream.Recv()
				if err != nil {
					if err == io.EOF {
						return nil
					}
					return fmt.Errorf("stream.Receive failed with %w", err)
				}
				zap.L().Debug("Received chunk", zap.Any("chunk", chunk))

				// merge chunk to chatReply
				payload := c.mergeChunkToChatReply(chunk)
				// send chatReply to frontend via eventbus
				rsp := &message.TaskResponse{
					IsQuit:  false,
					Payload: payload,
				}
				c.eventBus.EmitChatResponse(rsp)

				if chunk.IsEnd {
					// update ui
					event.EmitEvent(constant.EventSystemTrayMenuChange, task.StatusFinished)
					// close stream
					cancel()
					break
				}
			}
			return nil
		})

		err = g.Wait()
		if err != nil {
			cancel()
			if errors.Is(err, context.Canceled) {
				return nil
			}
			return fmt.Errorf("stream goroutines exited failed with %w", err)
		}
		return nil
	}

	err := interFunc()
	if err != nil {
		c.logger.Error("stream rpc failed", zap.Error(err))
		event.EmitEvent(constant.EventSystemTrayMenuChange, task.StatusError)
	}
	c.clearTaskCache()
}

func (c *AIClient) Close() {
	//close(c.sendCh)
	err := c.conn.Close()
	if err != nil {
		c.logger.Error("close rpc connect failed", zap.Error(err))
	}
	c.logger.Info("rpc client has stopped gracefully")
}

// mergeChunkToChatReply merge chunk to chatReply
func (c *AIClient) mergeChunkToChatReply(chunk *pb.ChatReply) emqx.TaskResponse {
	if c == nil || c.cache == nil {
		return emqx.TaskResponse{TaskId: c.currentTaskID, IsEnd: true, Status: emqx.FailedStatus}
	}
	c.cache.ParseAndReadChatReply(c.ctx, chunk, c.ossClient)
	return c.cache.TRsp
}

// setupTaskCache setup local task cache
func (c *AIClient) setupTaskCache(taskReq *emqx.TaskRequest) {
	if c == nil || taskReq == nil {
		return
	}
	if c.cache == nil {
		c.cache = &cache.TaskCache{
			TReq:    []emqx.TaskRequest{*taskReq},
			StepMap: map[string]*emqx.TaskStep{},
		}
		return
	}
	c.cache.TReq = append(c.cache.TReq, *taskReq)
}

// clearTaskCache clear the local task cache
func (c *AIClient) clearTaskCache() {
	if c == nil || c.cache == nil {
		return
	}
	err := c.cache.Reset()
	if err != nil {
		c.logger.Error("reset task cache failed", zap.Error(err))
		return
	}
}

func (c *AIClient) GetTaskID() string {
	return c.currentTaskID
}

// TriggerTask 触发任务执行
func (c *AIClient) TriggerTask(taskData map[string]interface{}) error {
	c.logger.Info("收到前端任务触发请求", zap.Any("taskData", taskData))

	// 从taskData中提取必要信息
	prompt, ok := taskData["prompt"].(string)
	if !ok {
		prompt = "执行任务" // 默认提示
	}

	// 创建任务请求
	taskRequest := message.NewTaskRequest(
		"",                                    // conversationId，空字符串会自动生成
		prompt,                                // prompt
		service.UserServiceImpl.GetDeviceID(), // deviceID
		emqx.START,                            // command type
		false,                                 // isQuit
		"",                                    // permissionValue
	)

	err := c.HandleBrokerChatRequest(taskRequest)
	if err != nil {
		c.logger.Error("触发任务失败", zap.Error(err))
		return err
	}

	return nil
}

func (c *AIClient) GetDeviceInfo() map[string]interface{} {
	return map[string]interface{}{
		"deviceID": service.UserServiceImpl.GetDeviceID(),
		"isLogin":  service.UserServiceImpl.IsLogin(),
		"userID":   service.UserServiceImpl.GetUserID(),
	}
}
