package gin

import (
	"AgenticAI-Client/pkg/apiserver/dto"
	"AgenticAI-Client/pkg/apiserver/engine/gin/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
)

func (g *Engine) CreateHandler() http.Handler {
	rg := g.Engine.Group("/auth")
	rg.GET("/login", g.login)
	rg.GET("/logout", g.logout)

	prg := g.Engine.Group("/permission")
	prg.GET("/check", g.permissionCheck)
	prg.PUT("/request", g.permissionRequest)
	prg.PUT("/to_access", g.permissionToAccess)
	prg.PUT("/to_screen", g.permissionToScreen)

	return g
}

func (g *Engine) login(ctx *gin.Context) {
	req := dto.LoginReq{
		Code:  ctx.Query("code"),
		State: ctx.Query("state"),
	}

	zap.L().Debug("login", zap.Any("LoginReq", req))
	rsp, err := g.userService.LoginHandler(ctx.Request.Context(), &req)
	response.JsonResponse(ctx, "登录成功", rsp, err)
}

func (g *Engine) logout(ctx *gin.Context) {
	req := dto.LogoutReq{}
	err := ctx.ShouldBind(&req)
	response.CheckError(ctx, http.StatusBadRequest, "logout", "validate", err)

	zap.L().Debug("logout", zap.Any("LogoutReq", req))
	rsp, err := g.userService.LogoutHandler(ctx.Request.Context(), &req)
	response.JsonResponse(ctx, "登出成功", rsp, err)
}

func (g *Engine) permissionCheck(ctx *gin.Context) {
	req := dto.Null{}
	zap.L().Debug("permissionCheck", zap.Any("Null", req))
	rsp, err := g.permissionService.PermissionCheck(ctx.Request.Context(), &req)
	response.JsonResponse(ctx, "permission", rsp, err)
}

func (g *Engine) permissionRequest(ctx *gin.Context) {
	req := dto.Null{}
	zap.L().Debug("permissionRequest", zap.Any("Null", req))
	rsp, err := g.permissionService.PermissionRequest(ctx.Request.Context(), &req)
	response.JsonResponse(ctx, "permission", rsp, err)
}

func (g *Engine) permissionToAccess(ctx *gin.Context) {
	req := dto.Null{}
	zap.L().Debug("PermissionToAccess", zap.Any("Null", req))
	rsp, err := g.permissionService.PermissionToAccess(ctx.Request.Context(), &req)
	response.JsonResponse(ctx, "permission", rsp, err)
}

func (g *Engine) permissionToScreen(ctx *gin.Context) {
	req := dto.Null{}
	zap.L().Debug("permissionToScreen", zap.Any("Null", req))
	rsp, err := g.permissionService.PermissionToScreen(ctx.Request.Context(), &req)
	response.JsonResponse(ctx, "permission", rsp, err)
}
