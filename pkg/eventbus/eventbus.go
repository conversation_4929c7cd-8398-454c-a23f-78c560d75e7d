package eventbus

import (
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/event"
	"AgenticAI-Client/pkg/workflow/message"
	"context"

	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
)

type Impl struct {
	ctx    context.Context
	cancel context.CancelFunc
	logger *zap.Logger

	// 处理器
	chatRequestHandler    func(request *message.TaskRequest) error
	connectRequestHandler func() error
	settingRequestHandler func(request *emqx.DeviceSettingsRequest) error
	quitAppHandler        func() error

	// 通道
	chatResponseCh    chan *message.TaskResponse
	deviceStatusCh    chan *emqx.DeviceResponse
	deviceHeartbeatCh chan *emqx.DeviceHeartbeatResponse
}

func NewEventBus(ctx context.Context) EventBus {
	ctx, cancel := context.WithCancel(ctx)

	impl := &Impl{
		ctx:    ctx,
		cancel: cancel,
		logger: zap.L().With(zap.String("module", "eventbus")),

		// 初始化默认处理器
		chatRequestHandler: func(request *message.TaskRequest) error {
			return nil
		},
		connectRequestHandler: func() error {
			return nil
		},
		settingRequestHandler: func(request *emqx.DeviceSettingsRequest) error {
			return nil
		},
		quitAppHandler: func() error {
			return nil
		},

		// 初始化通道
		chatResponseCh:    make(chan *message.TaskResponse, 10),
		deviceStatusCh:    make(chan *emqx.DeviceResponse, 10),
		deviceHeartbeatCh: make(chan *emqx.DeviceHeartbeatResponse, 10),
	}

	return impl
}

func (e *Impl) Run() {
	g := errgroup.Group{}

	// 处理聊天响应
	g.Go(func() error {
		for {
			select {
			case <-e.ctx.Done():
				e.logger.Info("exit chat response handler")
				return nil
			case response, ok := <-e.chatResponseCh:
				if !ok {
					continue
				}
				// 通过Wails event发送到前端
				event.EmitInnerUIEvent(constant.EventChatResponse, response)
			}
		}
	})

	// 处理设备状态
	g.Go(func() error {
		for {
			select {
			case <-e.ctx.Done():
				e.logger.Info("exit device status handler")
				return nil
			case status, ok := <-e.deviceStatusCh:
				if !ok {
					continue
				}
				// 通过Wails event发送到前端
				event.EmitInnerUIEvent(constant.EventDeviceStatus, status)
			}
		}
	})

	// 处理设备心跳
	g.Go(func() error {
		for {
			select {
			case <-e.ctx.Done():
				e.logger.Info("exit device heartbeat handler")
				return nil
			case heartbeat, ok := <-e.deviceHeartbeatCh:
				if !ok {
					continue
				}
				// 通过Wails event发送到前端
				event.EmitInnerUIEvent(constant.EventDeviceHeartbeat, heartbeat)
			}
		}
	})

	err := g.Wait()
	if err != nil {
		e.logger.Error("eventbus goroutines exited with error", zap.Error(err))
	}
}

func (e *Impl) Close() {
	e.cancel()
	close(e.chatResponseCh)
	close(e.deviceStatusCh)
	close(e.deviceHeartbeatCh)
	e.logger.Info("eventbus closed successfully")
}

func (e *Impl) EmitChatResponse(response *message.TaskResponse) {
	select {
	case e.chatResponseCh <- response:
	default:
		e.logger.Warn("chat response channel is full, dropping message")
	}
}

func (e *Impl) RegisterChatRequestHandler(handler func(request *message.TaskRequest) error) {
	e.chatRequestHandler = handler
}

func (e *Impl) RegisterConnectHandler(handler func() error) {
	e.connectRequestHandler = handler
}

func (e *Impl) EmitDeviceStatus(status *emqx.DeviceResponse) {
	select {
	case e.deviceStatusCh <- status:
	default:
		e.logger.Warn("device status channel is full, dropping message")
	}
}

func (e *Impl) EmitDeviceHeartbeat(heartbeat *emqx.DeviceHeartbeatResponse) {
	select {
	case e.deviceHeartbeatCh <- heartbeat:
	default:
		e.logger.Warn("device heartbeat channel is full, dropping message")
	}
}

func (e *Impl) RegisterSettingRequestHandler(handler func(request *emqx.DeviceSettingsRequest) error) {
	e.settingRequestHandler = handler
}

func (e *Impl) RegisterQuitAppHandler(handler func() error) {
	e.quitAppHandler = handler
}

func (e *Impl) Relink() error {
	// EventBus不需要重连，直接返回成功
	e.logger.Debug("eventbus relink called (no-op)")
	return nil
}
