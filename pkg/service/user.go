// Package service -----------------------------
// @file      : user.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/27 16:33
// -------------------------------------------
package service

import (
	"AgenticAI-Client/internal/auth"
	"AgenticAI-Client/pkg/utils"
	"encoding/json"
	"fmt"
	"github.com/Authing/authing-golang-sdk/v3/authentication"
	"os"
	"path/filepath"
)

type userInfo struct {
	Version       string                           `json:"version"`
	Path          string                           `json:"path"`
	UserID        string                           `json:"user_id"`
	Detail        auth.UserInfo                    `json:"detail"`
	Token         authentication.OIDCTokenResponse `json:"token"`
	Device        *DeviceInfo                      `json:"device"`
	IsShowWelcome bool                             `json:"is_show_welcome"`
}

func getUserInfo(dataDir string) (*userInfo, error) {
	// 定义设备信息文件的路径
	userInfoPath := filepath.Join(dataDir, "user_info_v1.json")

	// 检查设备信息文件是否存在
	var _userInfo userInfo
	_, err := os.Stat(userInfoPath)
	if err != nil {
		if !os.IsNotExist(err) {
			return nil, fmt.Errorf("无法检查用户信息文件是否存在: %w", err)
		}
		// 文件不存在，生成新的设备信息
		_userInfo = userInfo{
			Version:       "v1",
			Path:          userInfoPath,
			IsShowWelcome: true,
		}
	} else {
		// 文件存在，读取设备信息
		var data []byte
		data, err = os.ReadFile(userInfoPath)
		if err != nil {
			return nil, fmt.Errorf("无法读取用户信息文件: %w", err)
		}

		err = json.Unmarshal(data, &_userInfo)
		if err != nil {
			return nil, fmt.Errorf("无法解析用户信息: %w", err)
		}
		_userInfo.IsShowWelcome = false
	}

	// 更新设备信息
	var di *DeviceInfo
	di, err = NewDevice()
	if err != nil {
		return nil, fmt.Errorf("无法获取设备信息: %w", err)
	}
	_userInfo.Device = di

	err = _userInfo.Update()
	if err != nil {
		return nil, fmt.Errorf("无法更新用户信息: %w", err)
	}

	return &_userInfo, nil
}

func (p *userInfo) Update() error {
	if p == nil {
		return nil
	}

	data, err := json.Marshal(p)
	if err != nil {
		return fmt.Errorf("无法更新用户信息: %w", err)
	}

	err = os.WriteFile(p.Path, data, 0600)
	if err != nil {
		return fmt.Errorf("无法写入用户信息文件: %w", err)
	}

	return nil
}

func (p *userInfo) userName() string {
	if p == nil {
		return "未登录"
	}

	var name string
	if len(p.Detail.Nickname) > 0 {
		name = p.Detail.Nickname
	} else if len(p.Detail.Username) > 0 {
		name = p.Detail.Username
	} else if len(p.Detail.Name) > 0 {
		name = p.Detail.Name
	}

	return name
}

func (p *userInfo) String() string {
	// 用户名（手机号），这样看能不能放的下。放不下就优先用户名
	fir := p.userName()

	sec := utils.MaskPhone(p.Detail.PhoneNumber)
	if len(sec) == 0 {
		sec = p.Detail.Email
	}

	if len(fir) == 0 && len(sec) == 0 {
		return "未登录"
	}

	if len(fir) > 0 {
		return fir
	}

	return sec
}
